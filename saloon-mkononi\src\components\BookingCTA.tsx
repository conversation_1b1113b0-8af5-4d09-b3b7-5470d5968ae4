const BookingCTA = () => {
  return (
    <section className="py-16 lg:py-32">
      <div className="container mx-auto px-6 lg:px-12 flex flex-col md:flex-row items-center gap-12 lg:gap-20">
        <div className="w-full md:w-1/2">
          <h2 className="font-serif-display text-5xl lg:text-6xl leading-none">
            GET the <span className="italic">APP</span><br />TODAY<br />
            <span className="italic text-4xl lg:text-5xl">(and never wait again)</span>
          </h2>
          <p className="mt-8 text-xs leading-relaxed font-light text-gray-400 max-w-md">
            Your next appointment is just a download away. Get access to exclusive deals and top-tier salons in your area.
          </p>
          <p className="mt-4 text-xs leading-relaxed font-light text-gray-400 max-w-md">
            Join thousands of users who are booking smarter. Real-time availability means you can book on your schedule, not someone else's.
          </p>
          <div className="mt-10 space-y-0 max-w-md">
            <a href="#" className="flex items-center justify-between py-3.5 border-t border-b border-gray-700/50 group">
              <span className="text-[10px] uppercase tracking-wider">DOWNLOAD FOR IOS</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </a>
            <a href="#" className="flex items-center justify-between py-3.5 border-b border-gray-700/50 group">
              <span className="text-[10px] uppercase tracking-wider">GET IT ON ANDROID</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </a>
          </div>
        </div>
        <div className="w-full md:w-1/2 mt-8 md:mt-0">
          <img src="https://i.ibb.co/3WfK9Fh/crop-9.png" alt="Woman in a sparkly dress looking over her shoulder" className="w-full h-auto object-cover" />
        </div>
      </div>
    </section>
  )
}

export default BookingCTA
