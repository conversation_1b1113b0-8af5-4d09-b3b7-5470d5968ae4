const Header = () => {
  return (
    <header className="container mx-auto px-6 lg:px-12 py-6 lg:py-8">
      <nav className="flex justify-between items-center">
        <div className="hidden md:flex items-center space-x-10 text-[10px] uppercase tracking-wider">
          <a href="#" className="hover:text-gray-300 transition-colors">Home</a>
          <a href="#" className="hover:text-gray-300 transition-colors">About</a>
          <a href="#" className="hover:text-gray-300 transition-colors">Features</a>
        </div>
        <div className="text-center absolute left-1/2 -translate-x-1/2">
          <div className="font-serif-display text-2xl lg:text-2xl font-bold">SALOON</div>
          <div className="text-[9px] tracking-widest-plus mt-1">MKONONI</div>
        </div>
        <div className="hidden md:flex items-center space-x-10 text-[10px] uppercase tracking-wider">
          <a href="#" className="hover:text-gray-300 transition-colors">For Salons</a>
          <a href="#" className="hover:text-gray-300 transition-colors">Contact</a>
          <a href="#" className="border border-white/70 px-5 py-2 rounded-full hover:bg-white hover:text-black transition-colors duration-300 text-[10px]">Get The App</a>
        </div>
        {/* Mobile Menu Button */}
        <div className="md:hidden">
          <button className="text-white focus:outline-none">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16m-7 6h7"></path>
            </svg>
          </button>
        </div>
      </nav>
    </header>
  )
}

export default Header
