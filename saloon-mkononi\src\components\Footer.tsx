const Footer = () => {
  return (
    <footer className="pt-16 lg:pt-24 pb-8 border-t border-gray-800/50 mt-16">
      <div className="container mx-auto px-6 lg:px-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12 text-center md:text-left items-center">
          <div className="flex justify-center md:justify-start space-x-12 text-[11px] uppercase tracking-wider">
            <ul className="space-y-3">
              <li><a href="#" className="hover:text-gray-300">Home</a></li>
              <li><a href="#" className="hover:text-gray-300">Features</a></li>
              <li><a href="#" className="hover:text-gray-300">About</a></li>
            </ul>
            <ul className="space-y-3">
              <li><a href="#" className="hover:text-gray-300">Contact</a></li>
              <li><a href="#" className="hover:text-gray-300">For Salons</a></li>
              <li><a href="#" className="hover:text-gray-300">Privacy Policy</a></li>
            </ul>
          </div>
          <div className="flex flex-col items-center order-first md:order-none">
            <div className="font-serif-display text-2xl font-bold">SALOON</div>
            <div className="text-[9px] tracking-widest-plus mt-1">MKONONI</div>
          </div>
          <div className="flex flex-col items-center md:items-end">
            <p className="text-xs max-w-[200px] text-gray-400">Connecting you to the best salons in town, seamlessly.</p>
            <div className="flex space-x-3 mt-4">
              <a href="#" className="w-6 h-6 flex items-center justify-center border border-white/70 rounded-full p-1 hover:bg-white hover:text-black transition-colors">
                <svg className="w-3.5 h-3.5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.85s-.011 3.584-.069 4.85c-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07s-3.584-.012-4.85-.07c-3.252-.148-4.771-1.691-4.919-4.919-.058-1.265-.069-1.645-.069-4.85s.011-3.584.069-4.85c.149-3.225 1.664-4.771 4.919-4.919 1.266-.058 1.644-.07 4.85-.07zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948s.014 3.667.072 4.947c.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072s3.667-.014 4.947-.072c4.358-.2 6.78-2.618 6.98-6.98.059-1.281.073-1.689.073-4.948s-.014-3.667-.072-4.947c-.2-4.358-2.618-6.78-6.98-6.98-1.281-.058-1.689-.072-4.948-.072zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.162 6.162 6.162 6.162-2.759 6.162-6.162-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4s1.791-4 4-4 4 1.79 4 4-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44 1.441-.645 1.441-1.44-.645-1.44-1.441-1.44z"></path>
                </svg>
              </a>
              <a href="#" className="w-6 h-6 flex items-center justify-center border border-white/70 rounded-full p-1 hover:bg-white hover:text-black transition-colors">
                <svg className="w-3.5 h-3.5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"></path>
                </svg>
              </a>
              <a href="#" className="w-6 h-6 flex items-center justify-center border border-white/70 rounded-full p-1 hover:bg-white hover:text-black transition-colors">
                <svg className="w-3.5 h-3.5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616v.064c0 2.295 1.616 4.22 3.766 4.66-.499.135-.999.19-1.523.19-.247 0-.526-.023-.744-.065.625 1.877 2.445 3.252 4.604 3.288-1.705 1.34-3.858 2.135-6.191 2.135-.404 0-.802-.023-1.195-.069 2.212 1.422 4.833 2.246 7.633 2.246 9.16 0 14.164-7.604 13.97-14.164.002-.215.004-.428.012-.64z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-2 mt-20">
          <img src="https://i.ibb.co/qDq3sV8/footer-1.png" className="w-full h-32 md:h-40 object-cover" alt="model portfolio"/>
          <img src="https://i.ibb.co/6y4t41g/footer-2.png" className="w-full h-32 md:h-40 object-cover" alt="model portfolio"/>
          <img src="https://i.ibb.co/WfC91k4/footer-3.png" className="w-full h-32 md:h-40 object-cover" alt="model portfolio"/>
          <img src="https://i.ibb.co/7C96Tzq/footer-4.png" className="w-full h-32 md:h-40 object-cover" alt="model portfolio"/>
          <img src="https://i.ibb.co/5c37g2P/footer-5.png" className="w-full h-32 md:h-40 object-cover" alt="model portfolio"/>
          <img src="https://i.ibb.co/c8gHjWq/footer-6.png" className="w-full h-32 md:h-40 object-cover" alt="model portfolio"/>
        </div>

        <div className="text-center text-[9px] text-gray-500 mt-12 flex flex-col md:flex-row justify-between items-center gap-4">
          <p className="uppercase tracking-wider">&copy; SALOON MKONONI 2025 ALL RIGHTS RESERVED</p>
          <p className="uppercase tracking-wider">SITE DESIGN CREDIT</p>
          <a href="#" className="uppercase tracking-wider hover:text-white">BACK TO THE TOP</a>
        </div>
      </div>
    </footer>
  )
}

export default Footer
