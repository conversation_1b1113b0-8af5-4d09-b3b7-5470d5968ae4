const HowItWorks = () => {
  return (
    <section className="py-16 lg:py-24">
      <div className="container mx-auto px-6 lg:px-12">
        <h2 className="font-serif-display text-5xl lg:text-6xl italic text-center mb-20">HOW IT WORKS</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12 lg:gap-16">
          {/* Service 1 */}
          <div className="text-left">
            <div className="relative z-0">
              <img src="https://i.ibb.co/mS6M1qS/service-1.png" alt="Hair styling service" className="w-full h-auto object-cover"/>
              <span className="font-serif-display service-number">01</span>
            </div>
            <a href="#" className="flex items-center mt-6 text-[10px] uppercase tracking-wider group">
              DISCOVER & FILTER
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 ml-2 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </a>
          </div>
          {/* Service 2 */}
          <div className="text-left">
            <div className="relative z-0">
              <img src="https://i.ibb.co/k5J0P8Z/service-2.png" alt="Makeup and lashes service" className="w-full h-auto object-cover"/>
              <span className="font-serif-display service-number">02</span>
            </div>
            <a href="#" className="flex items-center mt-6 text-[10px] uppercase tracking-wider group">
              BOOK INSTANTLY
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 ml-2 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </a>
          </div>
          {/* Service 3 */}
          <div className="text-left">
            <div className="relative z-0">
              <img src="https://i.ibb.co/g9M4M2D/service-3.png" alt="Spa services" className="w-full h-auto object-cover"/>
              <span className="font-serif-display service-number">03</span>
            </div>
            <a href="#" className="flex items-center mt-6 text-[10px] uppercase tracking-wider group">
              RELAX & ENJOY
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 ml-2 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default HowItWorks
